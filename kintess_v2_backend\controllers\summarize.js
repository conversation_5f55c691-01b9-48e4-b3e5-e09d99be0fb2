const asyncHandler = require("express-async-handler");
const fs = require("fs");
const pdfParse = require("pdf-parse");
const mammoth = require("mammoth");
const { Configuration, OpenAIApi } = require("openai");

// Initialize OpenAI
const OpenAI = require("openai");

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY, // Make sure it's in .env
  timeout: 300_000,
});

const summarizeText = asyncHandler(async (req, res) => {
  if (!req.body.text && !req.file) {
    return res.status(400).json({ error: "No text or file provided" });
  }
  console.log("i am req.body", req.body);

  let extractedText = "";

  // ✅ Case 1: Simple text provided in body
  if (req.body.text) {
    extractedText = req.body.text;
  }

  // ✅ Case 2: File is uploaded
  else if (req.file) {
    const file = req.file;
    const filePath = file.path;
    const mimeType = file.mimetype;

    try {
      if (mimeType === "text/plain") {
        extractedText = fs.readFileSync(filePath, "utf-8");
      } else if (mimeType === "application/pdf") {
        const dataBuffer = fs.readFileSync(filePath);
        const pdfData = await pdfParse(dataBuffer);
        extractedText = pdfData.text;
      } else if (
        mimeType ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      ) {
        const result = await mammoth.extractRawText({ path: filePath });
        extractedText = result.value;
      } else {
        return res.status(400).json({ error: "Unsupported file type" });
      }
    } finally {
      // ✅ Clean up uploaded file
      fs.unlinkSync(filePath);
    }
  }

  // ❌ Case 3: Neither text nor file
  else {
    return res.status(400).json({ error: "No text or file provided" });
  }

  // 🔁 Send to OpenAI
  const prompt = `You are an expert summarization engine. Analyze the following text and return only the **shortened key takeaways** in bullet-point format.

Instructions:
- Output only the core ideas or main messages — nothing more.
- Keep each bullet extremely brief — one line if possible.
- Strictly avoid examples, stories, data points, or detailed explanations.
- Summarize aggressively: shorten as much as possible while retaining meaning.
- Use bullet points (•) for each takeaway.
- Do NOT copy or rephrase full sentences from the original text.
- Do NOT include introductory or closing remarks — only bullet points.
- The goal is to produce a minimal, high-signal summary of the content.

Text to summarize:
${extractedText}`;
  // console.log("Extracted text:", extractedText);

  // console.log("Prompt sent to OpenAI:", prompt);

  const gptResponse = await openai.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages: [{ role: "user", content: prompt }],
    temperature: 0.5,
  });

  const summarizedText = gptResponse.choices[0].message.content;

  // ✅ Return response
  res.status(200).json({ summary: summarizedText });
});

module.exports = {
  summarizeText,
};
