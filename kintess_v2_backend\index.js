const express = require("express");
const app = express();
const bodyParser = require("body-parser");
const dotenv = require("dotenv");
dotenv.config();
const connection = require("./config/connection");
const summarizeRoutes = require("./routes/summarize");
const cors = require("cors");

const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
// connection();
// Routes
app.use("/api", summarizeRoutes);

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});
