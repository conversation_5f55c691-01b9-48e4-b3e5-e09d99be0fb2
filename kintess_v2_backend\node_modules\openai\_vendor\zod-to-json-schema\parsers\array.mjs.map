{"version": 3, "file": "array.mjs", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/array.ts"], "names": [], "mappings": "OAAO,EAAe,qBAAqB,EAAE,MAAM,KAAK;OACjD,EAAiB,yBAAyB,EAAE;OAC5C,EAAmB,QAAQ,EAAE;AAWpC,MAAM,UAAU,aAAa,CAAC,GAAgB,EAAE,IAAU;IACxD,MAAM,GAAG,GAAyB;QAChC,IAAI,EAAE,OAAO;KACd,CAAC;IACF,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,KAAK,qBAAqB,CAAC,MAAM,EAAE,CAAC;QAC9D,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;YAClC,GAAG,IAAI;YACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC;SAC5C,CAAC,CAAC;IACL,CAAC;IAED,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;QAClB,yBAAyB,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC/F,CAAC;IACD,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;QAClB,yBAAyB,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC/F,CAAC;IACD,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;QACpB,yBAAyB,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACjG,yBAAyB,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACnG,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC"}