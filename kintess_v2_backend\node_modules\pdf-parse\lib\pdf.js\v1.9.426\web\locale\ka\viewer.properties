# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=წინა გვერდი
previous_label=წინა
next.title=შემდეგი გვერდი
next_label=შემდეგი

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=გვერდი
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}}-დან
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} {{pagesCount}}-დან)

zoom_out.title=დაშორება
zoom_out_label=დაშორება
zoom_in.title=მიახლოება
zoom_in_label=მიახლოება
zoom.title=ზომის ცვლილება
presentation_mode.title=პრეზენტაციის რეჟიმზე გადართვა
presentation_mode_label=პრეზენტაციის რეჟიმი
open_file.title=ფაილის გახსნა
open_file_label=გახსნა
print.title=დაბეჭდვა
print_label=დაბეჭდვა
download.title=ჩამოტვირთვა
download_label=ჩამოტვირთვა
bookmark.title=მიმდინარე ხედი (დაკოპირება ან გახსნა ახალ ფანჯარაში)
bookmark_label=მიმდინარე ხედი

# Secondary toolbar and context menu
tools.title=ხელსაწყოები
tools_label=ხელსაწყოები
first_page.title=პირველ გვერდზე გადასვლა
first_page.label=პირველ გვერდზე გადასვლა
first_page_label=პირველ გვერდზე გადასვლა
last_page.title=ბოლო გვერდზე გადასვლა
last_page.label=ბოლო გვერდზე გადასვლა
last_page_label=ბოლო გვერდზე გადასვლა
page_rotate_cw.title=ისრის მიმართულებით შებრუნება
page_rotate_cw.label=ისრის მიმართულებით შებრუნება
page_rotate_cw_label=ისრის მიმართულებით შებრუნება
page_rotate_ccw.title=ისრის საპირისპიროდ შებრუნება
page_rotate_ccw.label=ისრის საპირისპიროდ შებრუნება
page_rotate_ccw_label=ისრის საპირისპიროდ შებრუნება

cursor_text_select_tool.title=მოსანიშნი კურსორის ჩართვა
cursor_text_select_tool_label=მოსანიშნი კურსორი
cursor_hand_tool.title=გადასაადგილებელი კურსორის ჩართვა
cursor_hand_tool_label=გადასაადგილებელი კურსორი

# Document properties dialog box
document_properties.title=დოკუმენტის თვისებები…
document_properties_label=დოკუმენტის თვისებები…
document_properties_file_name=ფაილის სახელი:
document_properties_file_size=ფაილის ზომა:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} კბ ({{size_b}} ბაიტი)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} მბ ({{size_b}} ბაიტი)
document_properties_title=სათაური:
document_properties_author=ავტორი:
document_properties_subject=თემა:
document_properties_keywords=საკვანძო სიტყვები:
document_properties_creation_date=შექმნის თარიღი:
document_properties_modification_date=სახეცვალების თარიღი:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=შემქმნელი:
document_properties_producer=PDF მწარმოებელი:
document_properties_version=PDF ვერსია:
document_properties_page_count=გვერდების რაოდენობა:
document_properties_close=დახურვა

print_progress_message=დოკუმენტი მზადდება ამოსაბეჭდად…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=გაუქმება

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=გვერდითა ზოლის გამოჩენა
toggle_sidebar_notification.title=გვერდითა ზოლის ჩართვა/გამორთვა (დოკუმენტი შეიცავს მოხაზულობა/დანართს)
toggle_sidebar_label=გვერდითა ზოლის გამოჩენა
document_outline.title=დოკუმენტის მოხაზულობის ჩვენება (ორჯერ დაწკაპებით ყველა ელემენტის გაშლა/აკეცვა)
document_outline_label=დოკუმენტის მოხაზულობა
attachments.title=დანართების ჩვენება
attachments_label=დანართები
thumbs.title=ესკიზების ჩვენება
thumbs_label=ესკიზები
findbar.title=ძიება დოკუმენტში
findbar_label=ძიება

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=გვერდი {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=გვერდის ესკიზი {{page}}

# Find panel button title and messages
find_input.title=ძიება
find_input.placeholder=ძიება დოკუმენტში…
find_previous.title=ფრაზის წინა კონტექსტის პოვნა
find_previous_label=წინა
find_next.title=ფრაზის შემდეგი კონტექსტის პოვნა
find_next_label=შემდეგი
find_highlight=ყველას მონიშვნა
find_match_case_label=მთავრულის გათვალისწინება
find_reached_top=მიღწეულია დოკუმენტის ზედა წერტილამდე, გრძელდება ქვემოდან
find_reached_bottom=მიღწეულია დოკუმენტის ბოლო წერტილამდე, გრძელდება ზემოდან
find_not_found=კონტექსტი ვერ მოიძებნა

# Error panel labels
error_more_info=დამატებითი ინფორმაცია
error_less_info=ნაკლები ინფორმაცია
error_close=დახურვა
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=შეტყობინება: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=სტეკი: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ფაილი: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ხაზი: {{line}}
rendering_error=გვერდის რენდერისას დაფიქსირდა შეცდომა.

# Predefined zoom values
page_scale_width=გვერდის სიგანე
page_scale_fit=გვერდის მორგება
page_scale_auto=ზომის ავტომატური ცვლილება
page_scale_actual=აქტუალური ზომა
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=შეცდომა
loading_error=PDF-ის ჩატვირთვისას დაფიქსირდა შეცდომა.
invalid_file_error=არამართებული ან დაზიანებული PDF ფაილი.
missing_file_error=ნაკლული PDF ფაილი.
unexpected_response_error=სერვერის მოულოდნელი პასუხი.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} ანოტაცია]
password_label=შეიყვანეთ პაროლი, რათა გახსნათ ეს PDF ფაილი.
password_invalid=არასწორი პაროლი. გთხოვთ, სცადეთ ხელახლა.
password_ok=კარგი
password_cancel=გაუქმება

printing_not_supported=გაფრთხილება: ამ ბრაუზერის მიერ დაბეჭდვა ბოლომდე მხარდაჭერილი არაა.
printing_not_ready=გაფრთხილება: PDF ამობეჭდვისთვის ბოლომდე ჩატვირთული არაა.
web_fonts_disabled=ვებშრიფტები გამორთულია: ჩაშენებული PDF შრიფტების გამოყენება ვერ ხერხდება.
document_colors_not_allowed=PDF დოკუმენტებს არ აქვთ საკუთარი ფერების გამოყენების უფლება: ბრაუზერში გამორთულია "გვერდებისთვის საკუთარი ფერების გამოყენების უფლება".
